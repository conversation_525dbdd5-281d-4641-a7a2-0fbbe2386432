/**
 * Error types and utilities for the chat application
 */

export enum ErrorType {
  // Network and API errors
  NETWORK_ERROR = "NETWORK_ERROR",
  API_ERROR = "API_ERROR",
  TIMEOUT_ERROR = "TIMEOUT_ERROR",
  RATE_LIMIT_ERROR = "RATE_LIMIT_ERROR",
  
  // Authentication errors
  AUTH_ERROR = "AUTH_ERROR",
  UNAUTHORIZED_ERROR = "UNAUTHORIZED_ERROR",
  
  // Chat and AI errors
  AI_SERVICE_ERROR = "AI_SERVICE_ERROR",
  MESSAGE_SEND_ERROR = "MESSAGE_SEND_ERROR",
  MESSAGE_RENDER_ERROR = "MESSAGE_RENDER_ERROR",
  
  // Validation errors
  VALIDATION_ERROR = "VALIDATION_ERROR",
  INPUT_TOO_LONG = "INPUT_TOO_LONG",
  EMPTY_INPUT = "EMPTY_INPUT",
  
  // Router and navigation errors
  NAVIGATION_ERROR = "NAVIGATION_ERROR",
  ROUTE_NOT_FOUND = "ROUTE_NOT_FOUND",
  
  // General application errors
  UNKNOWN_ERROR = "UNKNOWN_ERROR",
  COMPONENT_ERROR = "COMPONENT_ERROR",
}

export interface AppError extends Error {
  type: ErrorType;
  code?: string;
  details?: Record<string, any>;
  retryable?: boolean;
  userMessage?: string;
}

export class ChatError extends Error implements AppError {
  public type: ErrorType;
  public code?: string;
  public details?: Record<string, any>;
  public retryable?: boolean;
  public userMessage?: string;

  constructor(
    type: ErrorType,
    message: string,
    options?: {
      code?: string;
      details?: Record<string, any>;
      retryable?: boolean;
      userMessage?: string;
      cause?: Error;
    }
  ) {
    super(message);
    this.name = "ChatError";
    this.type = type;
    this.code = options?.code;
    this.details = options?.details;
    this.retryable = options?.retryable ?? false;
    this.userMessage = options?.userMessage;
    
    if (options?.cause) {
      this.cause = options.cause;
    }
  }
}

export const createNetworkError = (originalError: Error): ChatError => {
  return new ChatError(
    ErrorType.NETWORK_ERROR,
    "Network connection failed",
    {
      retryable: true,
      userMessage: "Unable to connect to the server. Please check your internet connection and try again.",
      cause: originalError,
    }
  );
};

export const createTimeoutError = (): ChatError => {
  return new ChatError(
    ErrorType.TIMEOUT_ERROR,
    "Request timed out",
    {
      retryable: true,
      userMessage: "The request took too long to complete. Please try again.",
    }
  );
};

export const createRateLimitError = (retryAfter?: number): ChatError => {
  return new ChatError(
    ErrorType.RATE_LIMIT_ERROR,
    "Rate limit exceeded",
    {
      retryable: true,
      userMessage: retryAfter 
        ? `Too many requests. Please wait ${retryAfter} seconds before trying again.`
        : "Too many requests. Please wait a moment before trying again.",
      details: { retryAfter },
    }
  );
};

export const createAuthError = (message: string): ChatError => {
  return new ChatError(
    ErrorType.AUTH_ERROR,
    message,
    {
      retryable: false,
      userMessage: "Authentication failed. Please sign in again.",
    }
  );
};

export const createAIServiceError = (originalError: Error): ChatError => {
  return new ChatError(
    ErrorType.AI_SERVICE_ERROR,
    "AI service error",
    {
      retryable: true,
      userMessage: "The AI service is temporarily unavailable. Please try again in a moment.",
      cause: originalError,
    }
  );
};

export const createValidationError = (field: string, message: string): ChatError => {
  return new ChatError(
    ErrorType.VALIDATION_ERROR,
    `Validation failed for ${field}: ${message}`,
    {
      retryable: false,
      userMessage: message,
      details: { field },
    }
  );
};

export const createMessageRenderError = (originalError: Error): ChatError => {
  return new ChatError(
    ErrorType.MESSAGE_RENDER_ERROR,
    "Failed to render message",
    {
      retryable: true,
      userMessage: "Unable to display this message properly. Click to retry.",
      cause: originalError,
    }
  );
};

export const isRetryableError = (error: Error): boolean => {
  if (error instanceof ChatError) {
    return error.retryable ?? false;
  }
  
  // Check for common retryable error patterns
  const retryablePatterns = [
    /network/i,
    /timeout/i,
    /rate limit/i,
    /service unavailable/i,
    /internal server error/i,
  ];
  
  return retryablePatterns.some(pattern => pattern.test(error.message));
};

export const getUserFriendlyMessage = (error: Error): string => {
  if (error instanceof ChatError && error.userMessage) {
    return error.userMessage;
  }
  
  // Fallback messages for common error types
  if (error.message.toLowerCase().includes('network')) {
    return "Network connection failed. Please check your internet connection.";
  }
  
  if (error.message.toLowerCase().includes('timeout')) {
    return "Request timed out. Please try again.";
  }
  
  if (error.message.toLowerCase().includes('unauthorized')) {
    return "Authentication required. Please sign in.";
  }
  
  return "An unexpected error occurred. Please try again.";
};
