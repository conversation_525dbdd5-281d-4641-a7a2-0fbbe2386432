{"name": "chat-app", "version": "0.0.1", "private": true, "main": "expo-router/entry", "license": "MIT", "scripts": {"compile": "tsc --noEmit -p . --pretty", "lint": "eslint . --fix", "lint:check": "eslint .", "test": "jest", "test:watch": "jest --watch", "test:maestro": "maestro test -e MAESTRO_APP_ID=how.sowinghope.chatapp -e IS_DEV=true .maestro/flows", "test:maestro:ci": "maestro test -e MAESTRO_APP_ID=how.sowinghope.chatapp -e IS_DEV=false .maestro/flows", "adb": "adb reverse tcp:9090 tcp:9090 && adb reverse tcp:3000 tcp:3000 && adb reverse tcp:9001 tcp:9001 && adb reverse tcp:8081 tcp:8081", "build:ios:sim": "eas build --profile development --platform ios --local", "build:ios:dev": "eas build --profile development:device --platform ios --local", "build:ios:preview": "eas build --profile preview --platform ios --local", "build:ios:prod": "eas build --profile production --platform ios --local", "build:android:sim": "eas build --profile development --platform android --local", "build:android:dev": "eas build --profile development:device --platform android --local", "build:android:preview": "eas build --profile preview --platform android --local", "build:android:prod": "eas build --profile production --platform android --local", "start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "bundle:web": "npx expo export --platform web", "serve:web": "npx serve dist", "prebuild:clean": "npx expo prebuild --clean"}, "dependencies": {"@ai-sdk/google": "^1.2.19", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/react": "^1.2.12", "@clerk/clerk-expo": "^2.13.1", "@clerk/clerk-react": "^5.32.0", "@expo-google-fonts/space-grotesk": "^0.2.2", "@expo/metro-runtime": "~4.0.0", "@openrouter/ai-sdk-provider": "^0.7.2", "@react-native-async-storage/async-storage": "1.23.1", "@react-navigation/drawer": "^7.1.1", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "@rneui/themed": "^4.0.0-rc.8", "@sentry/react-native": "~6.10.0", "@shopify/flash-list": "1.7.3", "@stardazed/streams-text-encoding": "^1.0.2", "@ungap/structured-clone": "^1.3.0", "ai": "^4.3.16", "apisauce": "3.0.1", "convex": "^1.24.8", "date-fns": "^4.1.0", "expo": "^52.0.44", "expo-auth-session": "~6.0.3", "expo-build-properties": "~0.13.1", "expo-crypto": "~14.0.2", "expo-font": "~13.0.1", "expo-linking": "~7.0.2", "expo-localization": "~16.0.1", "expo-router": "~4.0.21", "expo-secure-store": "~14.0.1", "expo-splash-screen": "~0.29.10", "expo-status-bar": "~2.0.0", "expo-system-ui": "~4.0.3", "expo-web-browser": "~14.0.2", "i18next": "^23.14.0", "intl-pluralrules": "^2.0.1", "mobx": "6.10.2", "mobx-react-lite": "4.0.5", "mobx-state-tree": "5.3.0", "react": "18.3.1", "react-dom": "18.3.1", "react-i18next": "^15.0.1", "react-native": "0.76.9", "react-native-drawer-layout": "^4.0.1", "react-native-gesture-handler": "~2.20.2", "react-native-keyboard-controller": "^1.12.7", "react-native-marked": "^7.0.1", "react-native-mmkv": "3.1.0", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-web": "~0.19.6", "zod": "^3.25.67"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@testing-library/react-native": "^12.5.2", "@types/jest": "^29.2.1", "@types/react": "~18.3.12", "adb": "^0.2.0", "babel-jest": "^29.2.1", "eslint": "^8.57.0", "eslint-config-expo": "~8.0.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.0", "eslint-plugin-react-native": "^4.1.0", "eslint-plugin-reactotron": "^0.1.2", "jest": "^29.2.1", "jest-expo": "~52.0.1", "prettier": "^3.5.3", "react-test-renderer": "18.2.0", "reactotron-core-client": "^2.9.4", "reactotron-mst": "^3.1.7", "reactotron-react-js": "^3.3.11", "reactotron-react-native": "^5.0.5", "reactotron-react-native-mmkv": "^0.2.6", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "~5.3.3"}, "engines": {"node": "^18.18.0 || >=20.0.0"}}