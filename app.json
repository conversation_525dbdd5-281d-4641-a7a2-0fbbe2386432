{"expo": {"name": "ChatApp", "slug": "ChatApp", "owner": "mchisolm0", "scheme": "chatapp", "version": "1.0.0", "orientation": "portrait", "userInterfaceStyle": "automatic", "icon": "./assets/images/app-icon-all.png", "updates": {"fallbackToCacheTimeout": 0}, "newArchEnabled": true, "jsEngine": "hermes", "assetBundlePatterns": ["**/*"], "android": {"icon": "./assets/images/app-icon-android-legacy.png", "package": "how.sowinghope.chatapp", "adaptiveIcon": {"foregroundImage": "./assets/images/app-icon-android-adaptive-foreground.png", "backgroundImage": "./assets/images/app-icon-android-adaptive-background.png"}, "allowBackup": false}, "ios": {"icon": "./assets/images/app-icon-ios.png", "supportsTablet": true, "bundleIdentifier": "how.sowinghope.chatapp", "entitlements": {"com.apple.developer.applesignin": ["<PERSON><PERSON><PERSON>"]}, "infoPlist": {"ITSAppUsesNonExemptEncryption": false}}, "web": {"favicon": "./assets/images/app-icon-web-favicon.png", "bundler": "metro", "output": "server"}, "plugins": ["expo-localization", "expo-font", ["expo-splash-screen", {"image": "./assets/images/app-icon-android-adaptive-foreground.png", "imageWidth": 300, "resizeMode": "contain", "backgroundColor": "#191015"}], "expo-router", ["@sentry/react-native/expo", {"url": "https://sentry.io/", "project": "chat-app", "organization": "matthew-chisolm"}]], "experiments": {"tsconfigPaths": true, "typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "de4b266c-08a2-4544-8cea-1293f12f3687"}}}}